<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
// App component with router view
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #fafafa;
}

#app {
  width: 100%;
  min-height: 100vh;
}

/* Hide scrollbar for horizontal scrolling elements */
.promo-grid::-webkit-scrollbar,
.product-grid::-webkit-scrollbar {
  display: none;
}

.promo-grid,
.product-grid {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
