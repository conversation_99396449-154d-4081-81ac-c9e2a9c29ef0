<template>
  <div class="home-container">
    <!-- Header -->
    <div class="header">
      <h1 class="title">Shop</h1>
      <div class="search-container">
        <van-search
          v-model="searchValue"
          placeholder="Search products"
          shape="round"
          background="#EDEDED"
        />
      </div>
    </div>

    <!-- Promotional Banners -->
    <div class="promo-section">
      <div class="promo-grid">
        <div class="promo-card" v-for="promo in promoData" :key="promo.id">
          <div class="promo-image" :style="{ backgroundColor: promo.color }">
            <img :src="promo.image" :alt="promo.title" />
          </div>
          <div class="promo-text">{{ promo.title }}</div>
        </div>
      </div>
    </div>

    <!-- Flash Sales -->
    <div class="section">
      <h2 class="section-title">Flash Sales</h2>
      <div class="product-grid">
        <div
          class="product-card"
          v-for="product in flashSalesData"
          :key="product.id"
        >
          <div
            class="product-image"
            :style="{ backgroundColor: product.bgColor }"
          >
            <img :src="product.image" :alt="product.name" />
          </div>
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-price">${{ product.price }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Categories -->
    <div class="section">
      <h2 class="section-title">Categories</h2>
      <div class="categories-grid">
        <div
          class="category-row"
          v-for="(row, index) in categoriesRows"
          :key="index"
        >
          <div class="category-card" v-for="category in row" :key="category.id">
            <div
              class="category-image"
              :style="{ backgroundColor: category.bgColor }"
            >
              <img :src="category.image" :alt="category.name" />
            </div>
            <div class="category-name">{{ category.name }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recommended for You -->
    <div class="section">
      <h2 class="section-title">Recommended for You</h2>
      <div class="product-grid">
        <div
          class="product-card"
          v-for="product in recommendedData"
          :key="product.id"
        >
          <div
            class="product-image"
            :style="{ backgroundColor: product.bgColor }"
          >
            <img :src="product.image" :alt="product.name" />
          </div>
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-price">${{ product.price }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <van-tabbar v-model="activeTab" class="bottom-nav">
      <van-tabbar-item icon="home-o">Home</van-tabbar-item>
      <van-tabbar-item icon="search">Search</van-tabbar-item>
      <van-tabbar-item icon="shopping-cart-o">Cart</van-tabbar-item>
      <van-tabbar-item icon="user-o">Profile</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

const searchValue = ref("");
const activeTab = ref(0);

// Mock data for promotional banners
const promoData = ref([
  {
    id: 1,
    title: "Fashion Sale",
    image: "https://via.placeholder.com/150x135/FF6B6B/FFFFFF?text=Fashion",
    color: "#FF6B6B",
  },
  {
    id: 2,
    title: "Electronics Sale",
    image: "https://via.placeholder.com/150x135/4ECDC4/FFFFFF?text=Electronics",
    color: "#4ECDC4",
  },
  {
    id: 3,
    title: "Home Goods Sale",
    image: "https://via.placeholder.com/150x135/45B7D1/FFFFFF?text=Home",
    color: "#45B7D1",
  },
]);

// Mock data for flash sales
const flashSalesData = ref([
  {
    id: 1,
    name: "Running Shoes",
    price: "29.99",
    image: "https://via.placeholder.com/150x160/96CEB4/FFFFFF?text=Shoes",
    bgColor: "#96CEB4",
  },
  {
    id: 2,
    name: "Smart Watch",
    price: "79.99",
    image: "https://via.placeholder.com/150x160/FFEAA7/FFFFFF?text=Watch",
    bgColor: "#FFEAA7",
  },
  {
    id: 3,
    name: "Wireless Headphones",
    price: "49.99",
    image: "https://via.placeholder.com/150x160/DDA0DD/FFFFFF?text=Headphones",
    bgColor: "#DDA0DD",
  },
]);

// Mock data for categories
const categoriesData = ref([
  {
    id: 1,
    name: "Fashion",
    image: "https://via.placeholder.com/150x173/FF6B6B/FFFFFF?text=Fashion",
    bgColor: "#FF6B6B",
  },
  {
    id: 2,
    name: "Electronics",
    image: "https://via.placeholder.com/150x173/4ECDC4/FFFFFF?text=Electronics",
    bgColor: "#4ECDC4",
  },
  {
    id: 3,
    name: "Home & Garden",
    image: "https://via.placeholder.com/150x173/45B7D1/FFFFFF?text=Home",
    bgColor: "#45B7D1",
  },
  {
    id: 4,
    name: "Beauty",
    image: "https://via.placeholder.com/150x173/F39C12/FFFFFF?text=Beauty",
    bgColor: "#F39C12",
  },
  {
    id: 5,
    name: "Sports",
    image: "https://via.placeholder.com/150x173/E74C3C/FFFFFF?text=Sports",
    bgColor: "#E74C3C",
  },
  {
    id: 6,
    name: "Toys",
    image: "https://via.placeholder.com/150x173/9B59B6/FFFFFF?text=Toys",
    bgColor: "#9B59B6",
  },
  {
    id: 7,
    name: "Books",
    image: "https://via.placeholder.com/150x173/2ECC71/FFFFFF?text=Books",
    bgColor: "#2ECC71",
  },
  {
    id: 8,
    name: "Groceries",
    image: "https://via.placeholder.com/150x173/E67E22/FFFFFF?text=Groceries",
    bgColor: "#E67E22",
  },
  {
    id: 9,
    name: "Automotive",
    image: "https://via.placeholder.com/150x173/34495E/FFFFFF?text=Auto",
    bgColor: "#34495E",
  },
]);

// Group categories into rows of 2
const categoriesRows = computed(() => {
  const rows = [];
  for (let i = 0; i < categoriesData.value.length; i += 2) {
    rows.push(categoriesData.value.slice(i, i + 2));
  }
  return rows;
});

// Mock data for recommended products
const recommendedData = ref([
  {
    id: 1,
    name: "Coffee Maker",
    price: "39.99",
    image: "https://via.placeholder.com/150x160/8E44AD/FFFFFF?text=Coffee",
    bgColor: "#8E44AD",
  },
  {
    id: 2,
    name: "Yoga Mat",
    price: "19.99",
    image: "https://via.placeholder.com/150x160/16A085/FFFFFF?text=Yoga",
    bgColor: "#16A085",
  },
  {
    id: 3,
    name: "Bluetooth Speaker",
    price: "24.99",
    image: "https://via.placeholder.com/150x160/D35400/FFFFFF?text=Speaker",
    bgColor: "#D35400",
  },
]);
</script>

<style scoped>
.home-container {
  background-color: #fafafa;
  min-height: 100vh;
  padding-bottom: 60px; /* Space for bottom navigation */
}

.header {
  background-color: #fafafa;
  padding: 16px 16px 8px;
}

.title {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 1.28;
  text-align: center;
  color: #141414;
  margin: 0 0 12px 0;
  padding-left: 48px; /* Offset for potential menu icon */
}

.search-container {
  padding: 12px 0;
}

.promo-section {
  padding: 16px;
}

.promo-grid {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.promo-card {
  flex: 0 0 auto;
  width: 120px;
  border-radius: 8px;
  overflow: hidden;
}

.promo-image {
  height: 135px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.promo-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.promo-text {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
  color: #141414;
  text-align: left;
  margin-top: 8px;
}

.section {
  margin-bottom: 24px;
}

.section-title {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 1.28;
  color: #141414;
  margin: 0 0 8px 0;
  padding: 16px 16px 8px;
}

.product-grid {
  display: flex;
  gap: 12px;
  padding: 0 16px;
  overflow-x: auto;
}

.product-card {
  flex: 0 0 auto;
  width: 150px;
  border-radius: 8px;
  overflow: hidden;
}

.product-image {
  height: 160px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  padding: 8px 0;
}

.product-name {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
  color: #141414;
  margin-bottom: 4px;
}

.product-price {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5;
  color: #737373;
}

.categories-grid {
  padding: 0 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-row {
  display: flex;
  gap: 12px;
}

.category-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-bottom: 12px;
}

.category-image {
  height: 173px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-name {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
  color: #141414;
  text-align: left;
}

.bottom-nav {
  border-top: 1px solid #ededed;
}

/* Responsive adjustments */
@media (max-width: 390px) {
  .promo-card {
    width: 100px;
  }

  .product-card {
    width: 130px;
  }

  .product-image {
    height: 140px;
  }

  .category-image {
    height: 150px;
  }
}
</style>
