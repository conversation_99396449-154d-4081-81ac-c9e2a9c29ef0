{"name": "test-01", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.11.0", "pinia": "^3.0.3", "vant": "^4.9.21", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}