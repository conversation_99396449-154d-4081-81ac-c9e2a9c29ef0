<html>

<head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800" />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-neutral-50 justify-between group/design-root overflow-x-hidden" style='font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif;'>
        <div>
            <div class="flex items-center bg-neutral-50 p-4 pb-2 justify-between">
                <h2 class="text-[#141414] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pl-12">Shop</h2>
                <div class="flex w-12 items-center justify-end">
                    <button class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-12 bg-transparent text-[#141414] gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0">
                        <div class="text-[#141414]" data-icon="Microphone" data-size="24px" data-weight="regular">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                                <path d="M128,176a48.05,48.05,0,0,0,48-48V64a48,48,0,0,0-96,0v64A48.05,48.05,0,0,0,128,176ZM96,64a32,32,0,0,1,64,0v64a32,32,0,0,1-64,0Zm40,143.6V232a8,8,0,0,1-16,0V207.6A80.11,80.11,0,0,1,48,128a8,8,0,0,1,16,0,64,64,0,0,0,128,0,8,8,0,0,1,16,0A80.11,80.11,0,0,1,136,207.6Z"></path>
                            </svg>
                        </div>
                    </button>
                </div>
            </div>
            <div class="px-4 py-3">
                <label class="flex flex-col min-w-40 h-12 w-full">
                    <div class="flex w-full flex-1 items-stretch rounded-lg h-full">
                        <div class="text-neutral-500 flex border-none bg-[#ededed] items-center justify-center pl-4 rounded-l-lg border-r-0" data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                                <path d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"></path>
                            </svg>
                        </div>
                        <input placeholder="Search products" class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#141414] focus:outline-0 focus:ring-0 border-none bg-[#ededed] focus:border-none h-full placeholder:text-neutral-500 px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal" value="" />
                    </div>
                </label>
            </div>
            <div class="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&amp;::-webkit-scrollbar]:hidden">
                <div class="flex items-stretch p-4 gap-3">
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                        <div class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-lg flex flex-col" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCsNXzOd6vYXJ5bTI8qOyjRz8qaDQ_sJNfVPTGLR5RWOsfQWiFMSid0YpQUD5AHGRMTDZ_MzfGMu3UU8JEmz36e8-ZB6LSaqizuXvxuf03As1i4QGBLLkkUVT8VncBBOUKnI_WzY_KbV6NMhg5wcdcau8E5nQpI2yLbrVTz1uvjIxeUvIsGBZIN0T65pxTUdPqSJs-C9UVRsVal0ad2bT4_yzKftdJ94wI5cCgY9wgExT2fUqeVPA7fpXEvGKNxw1xP7GDYn0MeBzE");'></div>
                        <p class="text-[#141414] text-base font-medium leading-normal">Fashion Sale</p>
                    </div>
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                        <div class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-lg flex flex-col" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDM7H0acAt7bfuOUjRjpFZieKPYYoygxAsI2fv6nejf5LPyNHFT9YW-RLeahFX-UAkz8EAh3UIiDsLbtTd2OeYKTrhe0BfrOKTCDshj-8OOW2z8-t_BtSjqjUS7D2YEFM6MzS68m2hWFckAswEd3dcReo3yQ8mDT2f15qmyH7ASPAJ2pn2Q3HReOwkkUwcCQily5uaWpkvyU37G3TpNVwiuqOHpRAc0_qAkhsg35pv7Y8kMYuWaXtfutzc3qJ_kafgE6WumvORA8fk");'></div>
                        <p class="text-[#141414] text-base font-medium leading-normal">Electronics Sale</p>
                    </div>
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                        <div class="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-lg flex flex-col" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCnYCsrrFryQ2_-qxibnXKHHXIKHDT-t0DgQZdCGm3J9Dn1BkIN-pvheOBEjGh9AKQQgTVSRFJZ6_FTu5cK-FIHAyHER31n4m3RWAlJzCbz73JLmtaQc1BhA0-pBfXiIbvND6w0k7dROizPbT7hBhGIxo07ZbNoFyvsND6qN_81IgMVQzk52CmIh_xW7_yxeN9UlxTzg0sexkGtzd399GJSLuzGoDddvjP07-qL2PkTXnZzgCUlgd7G83jFz5BRf6hUUgHLNk9YGM8");'></div>
                        <p class="text-[#141414] text-base font-medium leading-normal">Home Goods Sale</p>
                    </div>
                </div>
            </div>
            <h3 class="text-[#141414] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Flash Sales</h3>
            <div class="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&amp;::-webkit-scrollbar]:hidden">
                <div class="flex items-stretch p-4 gap-3">
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-40">
                        <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAGhRZIEarFCqIZnTojOpvxjlunSd5EaRgDC7kS5lumjiw5SzZzJHChO2tGt213vzExJpyU9kYpmvJZgXRRosRKdsoe4yxVaTdFx9BJQlTqDak49OW2mQms9bj9DnfQUvGXEqnYI8cVgkCv_3B6vakoeFTJu0LLPTm2yIEO6M4Y7OquIQebRVWbyXp5p6A5o2igPNVnigdA2n1lJ7RL1DAK05N5XW-FVFfV8fcairIQuStQm_9EfmSt-l-QjkWSxfR_T0cattIjiW0");'></div>
                        <div>
                            <p class="text-[#141414] text-base font-medium leading-normal">Running Shoes</p>
                            <p class="text-neutral-500 text-sm font-normal leading-normal">$29.99</p>
                        </div>
                    </div>
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-40">
                        <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCPXatcMmC4gPPr9QUiLZTqu4PTt1CFtzsYdYc-OvSlGKi8AwfRMaUkj9hHkI1ce0YPhWElWQfCnnMCBiAGej8i8dt-7eAWODEykJ6jIzTjW6vWVIa8dvs9MyWTMVdOpDgN9FSnDPLYnPrmU-LmacfsdFO3tKzrkfGlULqld8toWjgUGKyFtilDfYbWUkIDoTCsoy2mYfeu8W9zkHaXwoX8nm7be7pQPNHHB6AVZrhrASBLS6NCuG4AOwzArBEyyU7SjQBkznQZ8LU");'></div>
                        <div>
                            <p class="text-[#141414] text-base font-medium leading-normal">Smart Watch</p>
                            <p class="text-neutral-500 text-sm font-normal leading-normal">$79.99</p>
                        </div>
                    </div>
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-40">
                        <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDQTV0DrH7cKo_T9vOHvzdMlr8Uf5KRuP8siL5MJ9Dy8jAcS1-EJhuM3qrhqK2iyQ7ZlOZGwnuZVPiv-hlhSwt37rWXdwc2_lbRl0KC1-NlaaUKnznoEM9rIHQu_5PAs8wSM5-OxvQyGwiDoXUSbszX7a6i8lXAGGkwAaSBeAVbO8mO6hW9K41RwEL1QwL0u3T9GtUdj0qFKmXbk7x79JJ1fuH7HyP_fUuflvyTjAVwbnAm8FlmIu3yyV2DiZX11DjQf_bTPDv9T8w");'></div>
                        <div>
                            <p class="text-[#141414] text-base font-medium leading-normal">Wireless Headphones</p>
                            <p class="text-neutral-500 text-sm font-normal leading-normal">$49.99</p>
                        </div>
                    </div>
                </div>
            </div>
            <h3 class="text-[#141414] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Categories</h3>
            <div class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
                <div class="flex flex-col gap-3 pb-3">
                    <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBIg2gySOI1wte-hsVzkBeQlU-a_7ANJRpzdCewV0axUQxREnKNlsEgxc9QgDNjtsi14XXYQrx5xlrRn1kxl4F31eMVfExQHZeDvAAJH77bS4f4SOFoYk9oGnfb9dZ5D_k2_e9fdzvdd7f3e8Pry1MPe8UHbwADoJKwyWnXKy0d6O_scnmQkROW1YsXTvEGDq-2hUskAs-V1g8lQjdSFYLeGfZzLzT3NqmRLEdO_sM-fqFTWASMYI-BmoNHiVyEuQD0R_yOqyZvnu8");'></div>
                    <p class="text-[#141414] text-base font-medium leading-normal">Fashion</p>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                    <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDqrleZDR85CzJ20XhDGbB6x16PHzcKHTTfnmOUax226jy8tvbEFtXhkqhkZuyfosD32Z17EFy_Wuvg8n09fya_7nQR1ZduTZ_xG2FiC_G173ZAhmAlByv4_LEDPLrdpi4qeoiNYDXvSPj8NaRUjtI61AE4r6n55_18WJEik4nuZ33wT41PSvhxBud5vQrgpiOAMZdLPA2CK_6-pKEqtmJLJwDIkUQ_d5vOTKAs1id-y7Eg3n2vYvCrpq573xkszmH8leg987PrsM0");'></div>
                    <p class="text-[#141414] text-base font-medium leading-normal">Electronics</p>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                    <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuByKrhW4BnlblIYYPxPhuoUH7mxZa5u2aNxAAzmi6fBinizsFBdvpV798sooWDdvA363EAivYM9UPrzgSXyNIYgrXU6SxVpN5jb8pbX78IO2Rp-DpCl05xibbRvTybwE5GJco-Yzq_hWg7PXh51nyCsFXt1vShuz97o0lY2ZG9K8eqPo7R1LcDcPIN1dffdSoG3jBT02SD9YXlgK5XVKvysvdtnXGvCHQX-o6sbU8cmfc2onAv3s3-nitED1ImnC-8WQqNtt1UhoIY");'></div>
                    <p class="text-[#141414] text-base font-medium leading-normal">Home &amp; Garden</p>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                    <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuALVnFzU2MiGrRFLS57jAJznsNuNUutfrNWslO3mbE8wE423bgThR9bOVj27MPblC0yg2_Gzgn3fhFD1hY7sU1u7t5qGrwaTb2LYTxgWg8TuKwSR1UyNEuYH8O0_vp7uedo-mQw1cKZPN9HzIHlfyZImTA2u4l5pBk8mKPztcyUdEQcTWc335oeY-D6sAlpjETQLyNVUAws-ya4Byau1AmC9WODd-EFPcUTIvxAnAeaSKwUnrwVXMkbWorlHcmBKNMu8jts9DpkgkY");'></div>
                    <p class="text-[#141414] text-base font-medium leading-normal">Beauty</p>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                    <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBrhR36xxsWsIrPzVsXXIrZR7zyoXHTqGY16r1Rkkjbzitpn_BmrY3OdYmVzoexpb710Apdsp_hsqAEp5hj-sL9S0LoAMX7uoO4c1K3sX3_jn7QzNOFsoGq9GhsidUmcdJ_JIM731mhM4jVxPJ-H22nUnGWrPlWydpiPbJAwrZybH7cBYsngHkB2_uXG0p7vgBaWylJ9m1Fq3MMqtnXmY9l0jrYKBrXmPlUqRKPBC99ZPGJVO-LKJq1gZqkxsMRXxYZSb_vDk5oPOg");'></div>
                    <p class="text-[#141414] text-base font-medium leading-normal">Sports</p>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                    <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBTizdpXFN7FxFHiUmy9mdtKyh8_DrJHv6FoRVS9a9Sp6jfg5hHlwB1STSqZPMciBUdRnc1GfyZVXSNwWr7uZqsUwQkrrVk0kB725PoapAmy-IlrZtws6e06dY1HEYFwx_WkY_u2vTou-zvEOe4_IGvR8LWGzUzhoPko5qUk-QEtFcsGwOtoaxULk60vbZyYFgJ2ZdA_N6lq_6zbIACdUt_FQBqoQLU2wFDKoet5pHVX3fy9AbrbupJCzmdlnfMDJGEJ1x560YGUw8");'></div>
                    <p class="text-[#141414] text-base font-medium leading-normal">Toys</p>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                    <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDLG_zKhW7x-5-qeecrgpQvE0_7Re_sjW4MasKEAguDSqIzGlPEz5_nJfVDWBrVTh-UcQN_MntU5fbfvGAQjSsthZ97ME_mYELShcGimLL-y1fXKSZub076g8wdBccJ6f7ASgyyLtpL5wDe798SqWQWr5FsQ0DBLteIW_8enZ8krrr4_FjoVjyweuUbjJmobye_ZJH3QSKrveZS-gttMcDjFhyGfJe8ffAosqBELhoDA7XK70TCc36pCsOp59mr5Blv6UU4XrP5Ta0");'></div>
                    <p class="text-[#141414] text-base font-medium leading-normal">Books</p>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                    <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDQd55q3EemOVsn5Wg11lc-oA4OdGMbqFGvR68HAQPRrEAq160CGLY9ofdbbN8ZKpKlHQJCnOW4bbFWGpvjcqnKS8FKm5Y-GI4mvtMtnmybt4QSQ5-4HKYnbWL2lnCR2Wq88-N-0QWdCrnJMxB6BiuoyiU__DZA-eyT_p_J5TbjqZLgchI6qukQZ8DAybTZOjY4V9YaVXgJrK3NMeIUz31Lj3mPGWxImgCPKbzdCc2G1YtvsIhvpZRwFsAU8-IWOBg5fQMpotVqQ6I");'></div>
                    <p class="text-[#141414] text-base font-medium leading-normal">Groceries</p>
                </div>
                <div class="flex flex-col gap-3 pb-3">
                    <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBLix9Xtrra1YEyQ-DJPQtYDFWEHWg5KXuI7TFCQqGwTKMuoFU5HpECzFcR_g1MPcJjy2YZaYAHfzn7IevRj_m5sZ90ZsBQgBg9PxMXuSDi6m3j3HlguBXWNpRQb6ayGXwqtBiMRusEtQY1brZYdQUxWeSq1Hoz2xVVneguK4euWEC5fF4PmYA7Tkba8M1P0WS3S-2iJgaUvktF4jxdT5uwcM27hfU_GmhZFbys1hHdSZP-LwSk8MgsGWOizxNjXzt7earjgmux9gw");'></div>
                    <p class="text-[#141414] text-base font-medium leading-normal">Automotive</p>
                </div>
            </div>
            <h3 class="text-[#141414] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Recommended for You</h3>
            <div class="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&amp;::-webkit-scrollbar]:hidden">
                <div class="flex items-stretch p-4 gap-3">
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-40">
                        <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAvqzx9_oOIQznOnFyLiY4QmN2cRBH6Ee3FvAomhSZSRJ4RNca1M3IdhZQ0MxdaOJTc5COSnxyzicLRBjqpfOH9jTn5P-j53erK5-2vQWUIePaCZN-QCpkLpctibR_YqSA-_Lq9cp6BxU98_I3mt1AwAt2idsHOxVqHT9xJqLC_5LOSmNShiG0Ge9mPSEnGa52Ah2mZYRSh35BLFMzsof-4jZ58TM0A7ckHYnVBY9gEZajxMa6kEmvWh4jk-mUGpxlWAFCK5Z4W8hc");'></div>
                        <div>
                            <p class="text-[#141414] text-base font-medium leading-normal">Coffee Maker</p>
                            <p class="text-neutral-500 text-sm font-normal leading-normal">$39.99</p>
                        </div>
                    </div>
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-40">
                        <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCOEfstiHOv8A0pJwmHMqCpJHQ8Hax1rN5lujsjyK7ie5SwpuqG43awlEHFTpx7kRgePfqgi7Dcc5a0lVFIjDbpEtk-esCN3ltUEEbnZQUnSa-oPZC5sftEWT8hBMRLIbbeZ0cELZY-X9hiqO7-ASLHCXclfaQJH8tXNyIiu2QAfkUDnRBmf_b02L8tRkuMosJcTnDRNhEfdz989RmQqmQ-q4R1ktBYtE4-iUueGLODORT_nXAKGIRg9gALI9YosqpLS13AMoAWf9k");'></div>
                        <div>
                            <p class="text-[#141414] text-base font-medium leading-normal">Yoga Mat</p>
                            <p class="text-neutral-500 text-sm font-normal leading-normal">$19.99</p>
                        </div>
                    </div>
                    <div class="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-40">
                        <div class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-lg flex flex-col" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAt88vC5mQ9qjghR68CLk4mQ0IGP7s6wgBZERjW88J8kEMe0U6QbC3ZQQy-_26BhV4fS5CQf4-GhTRi1pP-sj4o3yakZkNA-rwT5HgIlktto-_S-hw1xrYHfDg_wdqTxttkRsTOJBaszgO-pRnd4wvZnATWFh_abNJjBjuqlHcySc9Qal9x1D1pK-w-_kxzoI6_CNdA447pDt8R6Jq8uRpsTc23axXf6lR9M2P7yGTLpIfcmycu0_g_Xf_I0JWnAmpcmd7b3x_9II8");'></div>
                        <div>
                            <p class="text-[#141414] text-base font-medium leading-normal">Bluetooth Speaker</p>
                            <p class="text-neutral-500 text-sm font-normal leading-normal">$24.99</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div class="flex gap-2 border-t border-[#ededed] bg-neutral-50 px-4 pb-3 pt-2">
                <a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#141414]" href="#">
                    <div class="text-[#141414] flex h-8 items-center justify-center" data-icon="House" data-size="24px" data-weight="fill">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M224,115.55V208a16,16,0,0,1-16,16H168a16,16,0,0,1-16-16V168a8,8,0,0,0-8-8H112a8,8,0,0,0-8,8v40a16,16,0,0,1-16,16H48a16,16,0,0,1-16-16V115.55a16,16,0,0,1,5.17-11.78l80-75.48.11-.11a16,16,0,0,1,21.53,0,1.14,1.14,0,0,0,.11.11l80,75.48A16,16,0,0,1,224,115.55Z"></path>
                        </svg>
                    </div>
                </a>
                <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-neutral-500" href="#">
                    <div class="text-neutral-500 flex h-8 items-center justify-center" data-icon="ListBullets" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M80,64a8,8,0,0,1,8-8H216a8,8,0,0,1,0,16H88A8,8,0,0,1,80,64Zm136,56H88a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16Zm0,64H88a8,8,0,0,0,0,16H216a8,8,0,0,0,0-16ZM44,52A12,12,0,1,0,56,64,12,12,0,0,0,44,52Zm0,64a12,12,0,1,0,12,12A12,12,0,0,0,44,116Zm0,64a12,12,0,1,0,12,12A12,12,0,0,0,44,180Z"></path>
                        </svg>
                    </div>
                </a>
                <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-neutral-500" href="#">
                    <div class="text-neutral-500 flex h-8 items-center justify-center" data-icon="ShoppingCart" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M222.14,58.87A8,8,0,0,0,216,56H54.68L49.79,29.14A16,16,0,0,0,34.05,16H16a8,8,0,0,0,0,16h18L59.56,172.29a24,24,0,0,0,5.33,11.27,28,28,0,1,0,44.4,8.44h45.42A27.75,27.75,0,0,0,152,204a28,28,0,1,0,28-28H83.17a8,8,0,0,1-7.87-6.57L72.13,152h116a24,24,0,0,0,23.61-19.71l12.16-66.86A8,8,0,0,0,222.14,58.87ZM96,204a12,12,0,1,1-12-12A12,12,0,0,1,96,204Zm96,0a12,12,0,1,1-12-12A12,12,0,0,1,192,204Zm4-74.57A8,8,0,0,1,188.1,136H69.22L57.59,72H206.41Z"></path>
                        </svg>
                    </div>
                </a>
                <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-neutral-500" href="#">
                    <div class="text-neutral-500 flex h-8 items-center justify-center" data-icon="User" data-size="24px" data-weight="regular">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                            <path d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"></path>
                        </svg>
                    </div>
                </a>
            </div>
            <div class="h-5 bg-neutral-50"></div>
        </div>
    </div>
</body>

</html>